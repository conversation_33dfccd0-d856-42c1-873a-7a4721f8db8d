/* Toast 容器 */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: none;
}

/* Toast 基础样式 */
.toast {
  min-width: 300px;
  max-width: 500px;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Toast 进入动画 */
.toast-enter {
  transform: translateX(0);
  opacity: 1;
}

/* Toast 退出动画 */
.toast-exit {
  transform: translateX(100%);
  opacity: 0;
}

/* Toast 内容 */
.toast-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

/* Toast 图标 */
.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

/* Toast 消息 */
.toast-message {
  flex: 1;
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
}

/* Toast 关闭按钮 */
.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.toast-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
}

/* Toast 类型样式 */
.toast-success {
  border-left: 4px solid #10b981;
  background: linear-gradient(90deg, rgba(16, 185, 129, 0.1) 0%, var(--surface-color) 100%);
}

.toast-error {
  border-left: 4px solid #ef4444;
  background: linear-gradient(90deg, rgba(239, 68, 68, 0.1) 0%, var(--surface-color) 100%);
}

.toast-warning {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(90deg, rgba(245, 158, 11, 0.1) 0%, var(--surface-color) 100%);
}

.toast-info {
  border-left: 4px solid #3b82f6;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, var(--surface-color) 100%);
}

/* 复古主题适配 */
.retro-theme .toast {
  border-radius: 0;
  box-shadow: inset -1px -1px #0a0a0a, inset 1px 1px #dfdfdf, inset -2px -2px #808080, inset 2px 2px #ffffff;
  background: var(--surface-color);
}

.retro-theme .toast-close {
  font-family: 'MS Sans Serif', sans-serif;
  font-weight: bold;
}

.retro-theme .toast-close:hover {
  background: #c0c0c0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .toast {
    min-width: auto;
    max-width: none;
  }
  
  .toast-content {
    padding: 12px;
  }
  
  .toast-message {
    font-size: 13px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .toast {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .toast-message {
    color: #f9fafb;
  }
  
  .toast-close {
    color: #9ca3af;
  }
  
  .toast-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f9fafb;
  }
}
