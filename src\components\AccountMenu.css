/* 账户菜单容器 */
.account-menu {
  position: relative;
  display: inline-block;
}

/* 菜单触发器 */
.account-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  color: white;
}

.account-trigger:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.account-trigger:active {
  transform: translateY(0);
}

/* 用户头像 */
.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.avatar-icon {
  font-size: 16px;
  opacity: 0.9;
}

/* 用户信息 */
.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.welcome-text {
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.2;
}

.menu-arrow {
  font-size: 0.7rem;
  opacity: 0.7;
  transition: transform 0.3s ease;
}

/* 下拉菜单 */
.account-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  min-width: 280px;
  z-index: 1000;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 下拉菜单头部 */
.dropdown-header {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar-large {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.avatar-icon-large {
  font-size: 24px;
  opacity: 0.9;
}

.user-text {
  flex: 1;
}

.username {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 2px;
}

.user-email {
  font-size: 0.85rem;
  opacity: 0.8;
  word-break: break-all;
}

/* 分隔线 */
.dropdown-divider {
  height: 1px;
  background: #e9ecef;
  margin: 0;
}

/* 菜单项容器 */
.dropdown-menu {
  padding: 8px 0;
}

/* 菜单项 */
.menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  color: #495057;
  text-align: left;
}

.menu-item:hover {
  background: #f8f9fa;
  color: #212529;
}

.menu-icon {
  font-size: 1rem;
  width: 20px;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-weight: 500;
}

/* 特殊菜单项样式 */
.logout-item:hover {
  background: #fff3cd;
  color: #856404;
}

.delete-account-item:hover {
  background: #f8d7da;
  color: #721c24;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .account-dropdown {
    min-width: 260px;
    right: -20px;
  }
  
  .welcome-text {
    font-size: 0.8rem;
  }
  
  .user-avatar {
    width: 28px;
    height: 28px;
  }
  
  .avatar-icon {
    font-size: 14px;
  }
  
  .dropdown-header {
    padding: 12px;
  }
  
  .user-avatar-large {
    width: 40px;
    height: 40px;
  }
  
  .avatar-icon-large {
    font-size: 20px;
  }
  
  .username {
    font-size: 1rem;
  }
  
  .user-email {
    font-size: 0.8rem;
  }
  
  .menu-item {
    padding: 10px 12px;
    font-size: 0.85rem;
  }
}
