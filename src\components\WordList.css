.word-list-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.word-list-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #4CAF50;
  text-align: center;
}

.word-list-header h2 {
  margin: 0;
  color: #2e7d32;
  font-size: 1.8rem;
  font-weight: 700;
}

/* 搜索信息样式 */
.search-info {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9rem;
}

.search-term {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
}

.search-count {
  color: #6c757d;
  font-style: italic;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-state p {
  margin: 8px 0;
  font-size: 1.1rem;
}

/* 表格容器 */
.word-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  background: white;
}

/* 表格样式 */
.word-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
  background: white;
}

/* 表头样式 */
.word-table thead {
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

.word-table th {
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-right: 1px solid #dee2e6;
  white-space: nowrap;
}

.word-table th:last-child {
  border-right: none;
}

/* 表格行样式 */
.word-table tbody tr {
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.2s ease;
}

.word-table tbody tr:hover {
  background-color: #f8f9fa;
}

.word-table tbody tr:last-child {
  border-bottom: none;
}

/* 表格单元格样式 */
.word-table td {
  padding: 10px 8px;
  border-right: 1px solid #dee2e6;
  vertical-align: middle;
}

.word-table td:last-child {
  border-right: none;
}

/* 列宽设置 */
.col-original {
  width: 20%;
  min-width: 120px;
}

.col-pronunciation {
  width: 15%;
  min-width: 100px;
}

.col-translation {
  width: 20%;
  min-width: 120px;
}

.col-example {
  width: 30%;
  min-width: 150px;
}

.col-created {
  width: 10%;
  min-width: 80px;
}

.col-actions {
  width: 5%;
  min-width: 50px;
  text-align: center;
}

/* 文本样式 */
.original-text {
  font-weight: bold;
  color: #2c3e50;
  font-size: 1rem;
}

.pronunciation {
  color: #7f8c8d;
  font-style: italic;
  font-size: 0.9rem;
}

.translation {
  color: #27ae60;
  font-weight: 600;
  font-size: 1rem;
}

.example {
  color: #495057;
  font-size: 0.85rem;
  line-height: 1.4;
  cursor: help;
}

.created-date {
  color: #6c757d;
  font-size: 0.8rem;
  white-space: nowrap;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.favorite-btn {
  background: transparent;
  color: #ffc107;
  border: none;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 24px;
}

.favorite-btn:hover {
  background: rgba(255, 193, 7, 0.1);
  transform: scale(1.1);
}

.favorite-btn.favorited {
  color: #ffc107;
  text-shadow: 0 0 4px rgba(255, 193, 7, 0.5);
}

.edit-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.7rem;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 24px;
}

.edit-btn:hover {
  background: #0056b3;
  transform: scale(1.05);
}

.delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.7rem;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 24px;
}

.delete-btn:hover {
  background: #c82333;
  transform: scale(1.05);
}

/* 编辑模式样式 */
.edit-actions {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.save-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.7rem;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 24px;
}

.save-btn:hover {
  background: #1e7e34;
  transform: scale(1.05);
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.7rem;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 24px;
}

.cancel-btn:hover {
  background: #545b62;
  transform: scale(1.05);
}

.edit-input {
  width: 100%;
  padding: 2px 4px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 0.85rem;
  font-family: inherit;
}

.edit-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .word-list-container {
    padding: 16px;
  }

  .word-list-header h2 {
    font-size: 1.5rem;
  }

  .word-table-container {
    border-radius: 4px;
  }

  .word-table {
    font-size: 0.8rem;
  }

  .word-table th,
  .word-table td {
    padding: 8px 4px;
  }

  /* 隐藏例句列在小屏幕上 */
  .col-example {
    display: none;
  }

  .original-text {
    font-size: 0.9rem;
  }

  .pronunciation {
    font-size: 0.8rem;
  }

  .translation {
    font-size: 0.9rem;
  }

  .created-date {
    font-size: 0.7rem;
  }

  .delete-btn {
    min-width: 28px;
    height: 24px;
    font-size: 0.7rem;
  }
}
