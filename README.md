# 📚 多語言詞彙學習 (Multilingual Vocabulary)

> 簡潔高效的多語言詞彙管理應用，支持雲端同步

## 🌐 在線使用

**立即體驗：** [https://co2sou.github.io/wordList/](https://co2sou.github.io/wordList/)

## ✨ 主要功能

- **📝 詞彙管理**：添加、編輯、刪除詞彙，支持發音和例句
- **📊 學習統計**：實時顯示學習進度和詞彙統計
- **🌐 雲端同步**：多設備數據實時同步
- **🌍 多語言**：界面支持中英文切換
- **🎨 雙主題**：現代風格和復古 Windows 98 風格
- **📱 響應式**：完美適配手機、平板、電腦

## 🛠️ 技術棧

- **React 19** + **Vite** - 現代前端開發
- **Supabase** - 雲端數據庫和用戶認證
- **CSS3** - 原生樣式，響應式設計
- **GitHub Pages** - 靜態網站部署

## 🚀 快速開始

### 在線使用（推薦）
直接訪問：[https://co2sou.github.io/wordList/](https://co2sou.github.io/wordList/)

### 本地開發
```bash
git clone https://github.com/co2sou/wordList.git
cd wordList
npm install
npm run dev
```

## 📄 許可證

MIT License

---

**立即開始學習：** [https://co2sou.github.io/wordList/](https://co2sou.github.io/wordList/) 📚
