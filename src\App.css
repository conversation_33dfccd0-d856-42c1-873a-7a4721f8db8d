#root {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.app-header {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 32px 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-text {
  text-align: left;
}

.app-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.setup-btn,
.debug-btn,
.migration-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.setup-btn:hover,
.debug-btn:hover,
.migration-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.setup-btn.configured {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

.debug-btn {
  background: rgba(255, 193, 7, 0.2);
  border-color: rgba(255, 193, 7, 0.3);
}

.debug-btn:hover {
  background: rgba(255, 193, 7, 0.3);
  border-color: rgba(255, 193, 7, 0.5);
}

.migration-btn {
  background: rgba(255, 87, 34, 0.2);
  border-color: rgba(255, 87, 34, 0.3);
}

.migration-btn:hover {
  background: rgba(255, 87, 34, 0.3);
  border-color: rgba(255, 87, 34, 0.5);
}

.app-main {
  flex: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.app-footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 16px 24px;
  margin-top: auto;
}

.app-footer p {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.storage-info {
  font-size: 0.8rem !important;
  opacity: 0.6 !important;
}

.setup-instructions {
  background: #e3f2fd;
  border: 2px solid #2196F3;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.setup-instructions h3 {
  margin: 0 0 12px 0;
  color: #1976d2;
  font-size: 1.1rem;
}

.setup-instructions p {
  margin: 0 0 16px 0;
  color: #555;
  line-height: 1.5;
}

.setup-instructions details {
  margin-top: 12px;
}

.setup-instructions summary {
  cursor: pointer;
  font-weight: 600;
  color: #1976d2;
  padding: 8px 0;
}

.sql-code {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  color: #333;
  margin-top: 8px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .app-header {
    padding: 24px 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-text {
    text-align: center;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .setup-btn {
    padding: 10px 16px;
    font-size: 0.85rem;
  }

  .app-main {
    padding: 16px;
    gap: 16px;
  }

  .app-footer {
    padding: 12px 16px;
  }

  .setup-instructions {
    padding: 16px;
    margin-bottom: 16px;
  }

  .sql-code {
    font-size: 0.75rem;
    padding: 12px;
  }

  .user-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .welcome-text {
    font-size: 0.8rem;
  }
}

/* 加載界面 */
.loading-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 用戶信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.welcome-text {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

.user-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.delete-account-btn {
  background: rgba(220, 53, 69, 0.8);
  color: white;
  border: 1px solid rgba(220, 53, 69, 0.5);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.delete-account-btn:hover {
  background: rgba(220, 53, 69, 1);
  border-color: rgba(220, 53, 69, 0.8);
  transform: translateY(-1px);
}
