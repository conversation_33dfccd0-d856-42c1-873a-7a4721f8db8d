/* 搜索组件样式 */
.word-search {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.search-title {
  margin: 0 0 16px 0;
  color: #495057;
  font-size: 1.2rem;
  font-weight: 600;
}

.search-container {
  width: 100%;
}

.search-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 200px;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input::placeholder {
  color: #6c757d;
}

.search-buttons {
  display: flex;
  gap: 8px;
}

.search-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.search-btn:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.search-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.clear-search-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.clear-search-btn:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-1px);
}

.clear-search-btn:disabled {
  background: #adb5bd;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .word-search {
    padding: 16px;
    margin-bottom: 20px;
  }

  .search-title {
    font-size: 1.1rem;
    margin-bottom: 12px;
  }

  .search-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: unset;
    margin-bottom: 8px;
  }

  .search-buttons {
    justify-content: stretch;
  }

  .search-btn,
  .clear-search-btn {
    flex: 1;
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}
