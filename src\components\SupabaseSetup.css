.supabase-setup {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid #e3f2fd;
}

.supabase-setup.configured {
  border-color: #4CAF50;
  background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
}

.setup-header {
  text-align: center;
  margin-bottom: 24px;
}

.setup-header h2 {
  margin: 0 0 8px 0;
  color: #1976d2;
  font-size: 1.5rem;
}

.setup-header p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.config-status {
  text-align: center;
  padding: 20px;
}

.config-status h3 {
  margin: 0 0 12px 0;
  color: #4CAF50;
  font-size: 1.3rem;
}

.config-status p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 1rem;
}

.setup-instructions {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
  border-left: 4px solid #2196F3;
}

.setup-instructions h3 {
  margin: 0 0 12px 0;
  color: #1976d2;
  font-size: 1.1rem;
}

.setup-instructions ol {
  margin: 0;
  padding-left: 20px;
}

.setup-instructions li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.setup-instructions a {
  color: #2196F3;
  text-decoration: none;
  font-weight: 500;
}

.setup-instructions a:hover {
  text-decoration: underline;
}

.setup-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 6px;
  font-weight: 600;
  color: #555;
  font-size: 0.95rem;
}

.required {
  color: #e74c3c;
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #f44336;
  font-size: 0.9rem;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.save-btn,
.reset-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn {
  background-color: #2196F3;
  color: white;
  flex: 1;
}

.save-btn:hover:not(:disabled) {
  background-color: #1976d2;
  transform: translateY(-1px);
}

.save-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.reset-btn {
  background-color: #f8f9fa;
  color: #666;
  border: 2px solid #ddd;
  flex: 0 0 auto;
}

.reset-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.setup-note {
  margin-top: 20px;
  padding: 16px;
  background: #fff3e0;
  border-radius: 6px;
  border-left: 4px solid #ff9800;
}

.setup-note p {
  margin: 0;
  font-size: 0.9rem;
  color: #e65100;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .supabase-setup {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .setup-instructions {
    padding: 16px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .reset-btn {
    flex: 1;
  }
}
