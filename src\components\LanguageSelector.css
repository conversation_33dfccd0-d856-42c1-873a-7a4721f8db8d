.language-selector {
  position: relative;
  display: inline-block;
}

.language-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.language-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.language-flag {
  font-size: 1rem;
  line-height: 1;
}

.language-name {
  flex: 1;
  text-align: left;
}

.language-arrow {
  font-size: 0.7rem;
  transition: transform 0.3s ease;
  opacity: 0.8;
}

.language-arrow.open {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 1000;
  min-width: 140px;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: left;
}

.language-option:hover {
  background-color: #f8f9fa;
}

.language-option.active {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.language-option .language-flag {
  font-size: 1rem;
}

.language-option .language-name {
  flex: 1;
}

.language-check {
  color: #4CAF50;
  font-weight: bold;
  font-size: 0.8rem;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .language-button {
    padding: 6px 10px;
    font-size: 0.8rem;
    min-width: 80px;
  }
  
  .language-button .language-name {
    display: none;
  }
  
  .language-dropdown {
    min-width: 120px;
  }
  
  .language-option {
    padding: 10px 12px;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .language-button {
    padding: 6px 8px;
    min-width: 60px;
  }
  
  .language-arrow {
    display: none;
  }
}
