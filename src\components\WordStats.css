.word-stats {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid #e8f5e8;
}

.word-stats h2 {
  margin: 0 0 20px 0;
  color: #2e7d32;
  font-size: 1.5rem;
  text-align: center;
  border-bottom: 2px solid #4CAF50;
  padding-bottom: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 2px solid #dee2e6;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.total {
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
  border-color: #4CAF50;
}

.stat-card.today {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-color: #2196F3;
}

.stat-card.week {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
  border-color: #ff9800;
}

.stat-card.month {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
  border-color: #e91e63;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 600;
}

.monthly-stats {
  margin-top: 32px;
}

.monthly-stats h3 {
  margin: 0 0 20px 0;
  color: #2e7d32;
  font-size: 1.2rem;
  text-align: center;
}

.monthly-chart {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  overflow-x: auto;
  min-height: 200px;
}

.month-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
  flex-shrink: 0;
}

.bar-container {
  height: 120px;
  display: flex;
  align-items: flex-end;
  margin-bottom: 8px;
}

.bar {
  width: 24px;
  background: linear-gradient(to top, #4CAF50 0%, #81C784 100%);
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.bar:hover {
  background: linear-gradient(to top, #388E3C 0%, #66BB6A 100%);
  transform: scaleY(1.1);
}

.month-label {
  font-size: 0.75rem;
  color: #666;
  text-align: center;
  margin-bottom: 4px;
  font-weight: 500;
}

.month-count {
  font-size: 0.8rem;
  color: #4CAF50;
  font-weight: bold;
  text-align: center;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .word-stats {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 24px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .stat-label {
    font-size: 0.8rem;
  }
  
  .monthly-chart {
    gap: 8px;
    padding: 16px;
    min-height: 160px;
  }
  
  .month-bar {
    min-width: 50px;
  }
  
  .bar-container {
    height: 80px;
  }
  
  .bar {
    width: 20px;
  }
  
  .month-label {
    font-size: 0.7rem;
  }
  
  .month-count {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .monthly-chart {
    gap: 6px;
    padding: 12px;
  }
  
  .month-bar {
    min-width: 40px;
  }
  
  .bar {
    width: 16px;
  }
}
